# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Test suite for asyncpg migration from psycopg2.
This module tests the compatibility and functionality of the asyncpg-based database layer.
"""

import asyncio
import unittest
from unittest.mock import patch, MagicMock

from odoo.tests import common
from odoo.tests.common import TransactionCase


class TestAsyncpgMigration(TransactionCase):
    """Test asyncpg migration and compatibility."""

    def setUp(self):
        super().setUp()
        self.db_name = self.env.cr.dbname

    def test_async_db_connection(self):
        """Test that async database connections work correctly."""
        try:
            from odoo.async_sql_db import async_db_connect
            
            async def test_connection():
                connection = await async_db_connect(self.db_name, readonly=True)
                self.assertIsNotNone(connection)
                
                cursor = connection.cursor()
                self.assertIsNotNone(cursor)
                
                # Test basic query
                await cursor.execute("SELECT 1 as test_value")
                result = cursor.fetchone()
                self.assertIsNotNone(result)
                self.assertEqual(result[0], 1)
                
                await cursor.close()
                
            # Run the async test
            asyncio.run(test_connection())
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_sync_wrapper_compatibility(self):
        """Test that sync wrapper provides psycopg2 compatibility."""
        try:
            from odoo.async_migration_utils import create_sync_connection
            
            # Create sync connection wrapper
            connection = create_sync_connection(database=self.db_name)
            self.assertIsNotNone(connection)
            
            # Test cursor creation
            cursor = connection.cursor()
            self.assertIsNotNone(cursor)
            
            # Test basic query execution
            cursor.execute("SELECT 1 as test_value")
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result[0], 1)
            
            # Test fetchall
            cursor.execute("SELECT 1 as test_value UNION SELECT 2")
            results = cursor.fetchall()
            self.assertEqual(len(results), 2)
            
            cursor.close()
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_exception_compatibility(self):
        """Test that asyncpg exceptions are properly mapped to psycopg2 exceptions."""
        try:
            from odoo.async_sql_db import (
                Error, InterfaceError, DatabaseError, OperationalError
            )
            
            # Test that exception classes exist and are properly structured
            self.assertTrue(issubclass(InterfaceError, Error))
            self.assertTrue(issubclass(DatabaseError, Error))
            self.assertTrue(issubclass(OperationalError, DatabaseError))
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_cursor_methods_compatibility(self):
        """Test that cursor methods provide psycopg2 compatibility."""
        try:
            from odoo.async_migration_utils import create_sync_connection
            
            connection = create_sync_connection(database=self.db_name)
            cursor = connection.cursor()
            
            # Test execute with parameters
            cursor.execute("SELECT %s as test_value", (42,))
            result = cursor.fetchone()
            self.assertEqual(result[0], 42)
            
            # Test dictfetchone (Odoo extension)
            if hasattr(cursor, 'dictfetchone'):
                cursor.execute("SELECT 1 as test_col")
                dict_result = cursor.dictfetchone()
                self.assertIsInstance(dict_result, dict)
                self.assertEqual(dict_result['test_col'], 1)
            
            # Test rowcount
            cursor.execute("SELECT 1 UNION SELECT 2 UNION SELECT 3")
            cursor.fetchall()
            # Note: rowcount behavior may differ between psycopg2 and asyncpg
            
            cursor.close()
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_transaction_compatibility(self):
        """Test that transaction handling works correctly."""
        try:
            from odoo.async_migration_utils import create_sync_connection
            
            connection = create_sync_connection(database=self.db_name)
            cursor = connection.cursor()
            
            # Test context manager
            with cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertIsNotNone(result)
            
            # Cursor should be closed after context manager
            # Note: This behavior may differ between implementations
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_parameter_conversion(self):
        """Test that parameter conversion works correctly."""
        try:
            from odoo.async_sql_db import AsyncCursor
            
            # Test the parameter conversion method
            cursor = AsyncCursor(None, self.db_name, {})
            
            # Test positional parameters
            query, params = cursor._convert_query_and_params(
                "SELECT %s, %s", (1, 'test')
            )
            self.assertEqual(query, "SELECT $1, $2")
            self.assertEqual(params, [1, 'test'])
            
            # Test named parameters
            query, params = cursor._convert_query_and_params(
                "SELECT %(id)s, %(name)s", {'id': 1, 'name': 'test'}
            )
            self.assertEqual(query, "SELECT $1, $2")
            self.assertEqual(params, [1, 'test'])
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_odoo_model_operations(self):
        """Test that Odoo model operations work with asyncpg."""
        # Test basic model operations
        partner = self.env['res.partner'].create({
            'name': 'Test Partner for AsyncPG',
            'email': '<EMAIL>'
        })
        
        self.assertTrue(partner.id)
        self.assertEqual(partner.name, 'Test Partner for AsyncPG')
        
        # Test search
        found_partner = self.env['res.partner'].search([
            ('name', '=', 'Test Partner for AsyncPG')
        ])
        self.assertEqual(found_partner.id, partner.id)
        
        # Test update
        partner.write({'name': 'Updated Partner Name'})
        self.assertEqual(partner.name, 'Updated Partner Name')
        
        # Test delete
        partner.unlink()
        
        # Verify deletion
        found_partner = self.env['res.partner'].search([
            ('name', '=', 'Updated Partner Name')
        ])
        self.assertFalse(found_partner)

    def test_sql_injection_protection(self):
        """Test that SQL injection protection still works with asyncpg."""
        try:
            from odoo.async_migration_utils import create_sync_connection
            
            connection = create_sync_connection(database=self.db_name)
            cursor = connection.cursor()
            
            # Test that parameters are properly escaped
            malicious_input = "'; DROP TABLE res_partner; --"
            cursor.execute("SELECT %s as safe_value", (malicious_input,))
            result = cursor.fetchone()
            
            # The malicious input should be treated as a string value
            self.assertEqual(result[0], malicious_input)
            
            cursor.close()
            
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_performance_hooks(self):
        """Test that performance monitoring hooks still work."""
        # This test ensures that query counting and timing still work
        initial_count = getattr(self.env.cr, 'sql_log_count', 0)
        
        # Execute a query
        self.env.cr.execute("SELECT 1")
        
        # Check that query was counted
        final_count = getattr(self.env.cr, 'sql_log_count', 0)
        self.assertGreater(final_count, initial_count)


class TestAsyncpgPerformance(TransactionCase):
    """Test performance aspects of asyncpg migration."""

    def test_connection_pooling(self):
        """Test that connection pooling works correctly."""
        try:
            from odoo.async_sql_db import _AsyncPool, _AsyncPool_readonly
            
            # Test that pools are created
            if _AsyncPool is not None:
                self.assertIsNotNone(_AsyncPool)
            
            if _AsyncPool_readonly is not None:
                self.assertIsNotNone(_AsyncPool_readonly)
                
        except ImportError:
            self.skipTest("asyncpg not available")

    def test_batch_operations(self):
        """Test batch operations performance."""
        # Create multiple records to test batch performance
        partners_data = [
            {'name': f'Batch Partner {i}', 'email': f'batch{i}@test.com'}
            for i in range(10)
        ]
        
        # Test batch creation
        partners = self.env['res.partner'].create(partners_data)
        self.assertEqual(len(partners), 10)
        
        # Test batch update
        partners.write({'active': False})
        
        # Verify all are inactive
        for partner in partners:
            self.assertFalse(partner.active)
        
        # Clean up
        partners.unlink()


if __name__ == '__main__':
    unittest.main()
