# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Odoo ASGI application

This module provides the ASGI application for Odoo, replacing the legacy WSGI
implementation with modern async capabilities while maintaining backward compatibility.
"""

import asyncio
import logging
import threading
import time
import os
from contextlib import asynccontextmanager
from typing import Dict, Any, Callable, Awaitable, Optional

from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request as StarletteRequest
from starlette.responses import Response as StarletteResponse, FileResponse, RedirectResponse
from starlette.routing import Route, Mount
from starlette.staticfiles import StaticFiles

import odoo
from odoo import http, api, modules
from odoo.tools import config
from odoo.tools.func import lazy_property
from odoo.exceptions import UserError, AccessError
from odoo.http import SessionExpiredException
from werkzeug.routing import Map, Rule
from werkzeug.exceptions import NotFound

_logger = logging.getLogger(__name__)


class ASGIRequest:
    """
    ASGI Request wrapper that maintains compatibility with existing Odoo Request interface
    """
    
    def __init__(self, starlette_request: StarletteRequest):
        self.starlette_request = starlette_request
        self._setup_compatibility()
    
    def _setup_compatibility(self):
        """Setup compatibility attributes for existing Odoo code"""
        # Create a mock HTTPRequest that mimics the old interface
        self.httprequest = self._create_http_request_mock()
        self.future_response = http.FutureResponse()
        self.dispatcher = http._dispatchers['http'](self)
        
        # Setup other required attributes
        self.geoip = http.GeoIP(self.starlette_request.client.host if self.starlette_request.client else '127.0.0.1')
        self.registry = None
        self.env = None
        self.params = dict(self.starlette_request.query_params)
        self.session = None
        self.db = None
    
    def _create_http_request_mock(self):
        """Create a mock HTTPRequest object for compatibility"""
        class HTTPRequestMock:
            def __init__(self, starlette_req):
                self.starlette_req = starlette_req
                self.method = starlette_req.method
                self.url = str(starlette_req.url)
                self.path = starlette_req.url.path
                self.query_string = starlette_req.url.query.encode() if starlette_req.url.query else b''
                self.headers = dict(starlette_req.headers)
                self.remote_addr = starlette_req.client.host if starlette_req.client else '127.0.0.1'
                self.args = dict(starlette_req.query_params)
                self.cookies = dict(starlette_req.cookies)
                
            @property
            def mimetype(self):
                return self.headers.get('content-type', '').split(';')[0]
        
        return HTTPRequestMock(self.starlette_request)
    
    async def _post_init(self):
        """Async version of post init"""
        self.session, self.db = await self._get_session_and_dbname()

        # Update params with form data if it's a POST request
        if self.httprequest.method == 'POST':
            try:
                form_data = await self.starlette_request.form()
                self.params.update(dict(form_data))
            except Exception:
                # If form parsing fails, try JSON
                try:
                    json_data = await self.starlette_request.json()
                    if isinstance(json_data, dict):
                        self.params.update(json_data)
                except Exception:
                    pass
    
    async def _get_session_and_dbname(self):
        """Async version of session and db name retrieval"""
        # Get session from cookies
        session_id = self.httprequest.cookies.get('session_id')
        session = None
        db = None

        if session_id:
            try:
                # Load session from session store
                session_store = http.root.session_store
                session = session_store.get(session_id)
                if session and hasattr(session, 'db'):
                    db = session.db
            except Exception:
                _logger.debug("Failed to load session", exc_info=True)

        # Try to get db from URL parameters
        if not db:
            db = self.httprequest.args.get('db')
            if db:
                # Validate db against available databases
                try:
                    available_dbs = http.db_list()
                    if db not in available_dbs:
                        db = None
                except Exception:
                    db = None

        # If still no db, try to use single database mode
        if not db:
            try:
                available_dbs = http.db_list()
                if len(available_dbs) == 1:
                    db = available_dbs[0]
            except Exception:
                pass

        return session, db

    def _set_request_dispatcher(self, rule):
        """Set the request dispatcher based on the routing rule"""
        # This mimics the behavior from the WSGI implementation
        if hasattr(rule, 'endpoint') and hasattr(rule.endpoint, 'routing'):
            routing = rule.endpoint.routing
            if 'type' in routing:
                dispatcher_type = routing['type']
                if dispatcher_type in http._dispatchers:
                    self.dispatcher = http._dispatchers[dispatcher_type](self)


class OdooASGIMiddleware(BaseHTTPMiddleware):
    """
    Main ASGI middleware that handles Odoo request processing
    """
    
    def __init__(self, app, odoo_app):
        super().__init__(app)
        self.odoo_app = odoo_app
    
    async def dispatch(self, request: StarletteRequest, call_next: Callable) -> StarletteResponse:
        """
        Main request dispatch method
        """
        # Setup thread-local variables for compatibility
        current_thread = threading.current_thread()
        current_thread.query_count = 0
        current_thread.query_time = 0
        current_thread.perf_t0 = time.time()
        current_thread.cursor_mode = None
        
        # Clean up thread-local attributes
        for attr in ['dbname', 'uid']:
            if hasattr(current_thread, attr):
                delattr(current_thread, attr)
        
        try:
            # Create Odoo request wrapper
            odoo_request = ASGIRequest(request)
            
            # Setup request context
            http._request_stack.push(odoo_request)
            
            try:
                await odoo_request._post_init()
                current_thread.url = str(request.url)
                
                # Handle static files
                if self.odoo_app.get_static_file(request.url.path):
                    return await self._serve_static(odoo_request)
                
                # Handle database requests
                elif odoo_request.db:
                    return await self._serve_db(odoo_request)
                else:
                    return await self._serve_nodb(odoo_request)
                    
            finally:
                http._request_stack.pop()
                
        except Exception:
            _logger.error("Exception during ASGI request handling.", exc_info=True)
            return StarletteResponse("Internal Server Error", status_code=500)
    
    async def _serve_static(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle static file serving"""
        file_path = self.odoo_app.get_static_file(odoo_request.httprequest.path)
        if file_path and os.path.isfile(file_path):
            return FileResponse(file_path)
        return StarletteResponse("File not found", status_code=404)

    async def _serve_db(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle database requests"""
        try:
            # For now, redirect to the traditional WSGI handling
            # This is a placeholder - full async DB implementation would go here
            return RedirectResponse(url="/web", status_code=302)
        except Exception:
            _logger.exception("Error serving database request")
            return StarletteResponse("Internal Server Error", status_code=500)

    async def _serve_nodb(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle no-database requests"""
        try:
            # Use the same routing logic as WSGI version
            router = http.root.nodb_routing_map.bind_to_environ(self._create_environ(odoo_request))
            try:
                rule, args = router.match(return_rule=True)
                odoo_request._set_request_dispatcher(rule)

                # Pre-dispatch
                odoo_request.dispatcher.pre_dispatch(rule, args)

                # Dispatch to endpoint
                response = odoo_request.dispatcher.dispatch(rule.endpoint, args)

                # Post-dispatch
                odoo_request.dispatcher.post_dispatch(response)

                # Convert response to ASGI format
                return await self._convert_response_to_asgi(response)

            except NotFound:
                # If no route found, redirect to database selector
                return RedirectResponse(url="/web/database/selector", status_code=303)

        except Exception:
            _logger.exception("Error serving no-database request")
            return StarletteResponse("Internal Server Error", status_code=500)

    def _create_environ(self, odoo_request: ASGIRequest) -> dict:
        """Create WSGI environ dict from ASGI request for compatibility"""
        environ = {
            'REQUEST_METHOD': odoo_request.httprequest.method,
            'PATH_INFO': odoo_request.httprequest.path,
            'QUERY_STRING': odoo_request.httprequest.query_string.decode() if odoo_request.httprequest.query_string else '',
            'CONTENT_TYPE': odoo_request.httprequest.headers.get('content-type', ''),
            'CONTENT_LENGTH': odoo_request.httprequest.headers.get('content-length', ''),
            'SERVER_NAME': odoo_request.starlette_request.url.hostname or 'localhost',
            'SERVER_PORT': str(odoo_request.starlette_request.url.port or 80),
            'HTTP_HOST': odoo_request.httprequest.headers.get('host', 'localhost'),
            'wsgi.url_scheme': odoo_request.starlette_request.url.scheme,
        }

        # Add HTTP headers
        for key, value in odoo_request.httprequest.headers.items():
            key = 'HTTP_' + key.upper().replace('-', '_')
            environ[key] = value

        return environ

    async def _convert_response_to_asgi(self, response):
        """Convert Odoo response to ASGI response"""
        if hasattr(response, 'status_code'):
            # It's already a proper response object
            if hasattr(response, 'headers'):
                headers = dict(response.headers)
            else:
                headers = {}

            if hasattr(response, 'data'):
                content = response.data
            elif hasattr(response, 'get_data'):
                content = response.get_data()
            else:
                content = str(response)

            return StarletteResponse(
                content=content,
                status_code=response.status_code,
                headers=headers
            )
        else:
            # Simple string response
            return StarletteResponse(content=str(response))


class ASGIApplication:
    """
    Modern ASGI application for Odoo
    """
    
    def __init__(self):
        self.starlette_app = None
        self._setup_application()
    
    def _setup_application(self):
        """Setup the Starlette ASGI application"""
        middleware = [
            Middleware(OdooASGIMiddleware, odoo_app=self)
        ]
        
        routes = [
            # Add routes here as needed
        ]
        
        self.starlette_app = Starlette(
            routes=routes,
            middleware=middleware
        )
    
    @lazy_property
    def statics(self):
        """
        Map module names to their absolute ``static`` path on the file
        system. (Copied from original Application class)
        """
        from odoo.modules.module import get_manifest
        from os.path import join as opj
        import os
        
        mod2path = {}
        for addons_path in odoo.addons.__path__:
            for module in os.listdir(addons_path):
                manifest = get_manifest(module)
                static_path = opj(addons_path, module, 'static')
                if (manifest
                        and (manifest['installable'] or manifest['assets'])
                        and os.path.isdir(static_path)):
                    mod2path[module] = static_path
        return mod2path
    
    def get_static_file(self, url, host=''):
        """
        Get the full-path of the file if the url resolves to a local
        static file, otherwise return None. (Copied from original Application class)
        """
        from urllib.parse import urlparse
        
        netloc, path = urlparse(url)[1:3]
        try:
            path_netloc, module, static, resource = path.split('/', 3)
        except ValueError:
            return None

        if ((netloc and netloc != host) or (path_netloc and path_netloc != host)):
            return None

        if (module not in self.statics or static != 'static' or not resource):
            return None

        try:
            import os
            resource_path = os.path.join(self.statics[module], resource)
            if os.path.isfile(resource_path):
                return resource_path
        except (OSError, ValueError):
            pass
        return None
    
    async def __call__(self, scope, receive, send):
        """
        ASGI application entry point
        """
        await self.starlette_app(scope, receive, send)


# Create the global ASGI application instance
asgi_root = ASGIApplication()
