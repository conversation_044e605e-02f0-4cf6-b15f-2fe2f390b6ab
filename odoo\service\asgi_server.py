# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
ASGI Server implementations for Odoo

This module provides ASGI server classes that replace the legacy WSGI servers
with modern async capabilities while maintaining the same interface and functionality.
"""

import asyncio
import logging
import multiprocessing
import os
import signal
import threading
import time
from typing import Optional, Dict, Any

import uvicorn
from uvicorn.config import Config
from uvicorn.server import Server

from odoo.tools import config
from odoo.service.server import CommonServer

_logger = logging.getLogger(__name__)


class ASGIServerMixin:
    """
    Common functionality for ASGI servers
    """
    def __init__(self, app):
        super().__init__(app)
        self.uvicorn_server = None
        self.uvicorn_config = None
        self._setup_uvicorn_config()
    
    def _setup_uvicorn_config(self):
        """Setup Uvicorn configuration"""
        self.uvicorn_config = Config(
            app=self.app,
            host=self.interface,
            port=self.port,
            log_level="info",
            access_log=True,
            use_colors=False,
            server_header=False,
            date_header=False,
        )
        self.uvicorn_server = Server(self.uvicorn_config)
    
    async def _serve_async(self):
        """Async server serving method"""
        try:
            await self.uvicorn_server.serve()
        except Exception:
            _logger.exception("Exception in ASGI server")
            raise


class ThreadedASGIServer(ASGIServerMixin, CommonServer):
    """
    Threaded ASGI server - equivalent to ThreadedServer
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.thread = None
        self.loop = None
    
    def start(self):
        """Start the threaded ASGI server"""
        _logger.info('ASGI HTTP service running on %s:%s', self.interface, self.port)
        
        def run_server():
            # Create new event loop for this thread
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            try:
                self.loop.run_until_complete(self._serve_async())
            except Exception:
                _logger.exception("Exception in threaded ASGI server")
            finally:
                self.loop.close()
        
        self.thread = threading.Thread(target=run_server, name="odoo.service.asgi.httpd", daemon=True)
        self.thread.start()
    
    def stop(self):
        """Stop the threaded ASGI server"""
        if self.uvicorn_server:
            if self.loop and self.loop.is_running():
                # Schedule shutdown in the server's event loop
                asyncio.run_coroutine_threadsafe(self.uvicorn_server.shutdown(), self.loop)
            
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        super().stop()
    
    def run(self, preload, stop):
        """Run the server"""
        if stop:
            return 0
        
        self.start()
        
        try:
            # Wait for the server thread
            if self.thread:
                self.thread.join()
        except KeyboardInterrupt:
            _logger.info("Keyboard interrupt received")
        finally:
            self.stop()
        
        return 0


class GeventASGIServer(ASGIServerMixin, CommonServer):
    """
    Gevent-compatible ASGI server - equivalent to GeventServer
    Note: This uses asyncio instead of gevent for better ASGI compatibility
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.loop = None
    
    def start(self):
        """Start the gevent-style ASGI server"""
        _logger.info('ASGI Evented Service running on %s:%s', self.interface, self.port)
        
        # Use asyncio instead of gevent for better ASGI support
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self._serve_async())
        except Exception:
            _logger.exception("Exception in evented ASGI server")
            raise
        finally:
            self.loop.close()
    
    def stop(self):
        """Stop the gevent-style ASGI server"""
        if self.uvicorn_server and self.loop:
            if self.loop.is_running():
                asyncio.run_coroutine_threadsafe(self.uvicorn_server.shutdown(), self.loop)
        
        super().stop()
    
    def run(self, preload, stop):
        """Run the server"""
        if stop:
            return 0
        
        try:
            self.start()
        except KeyboardInterrupt:
            _logger.info("Keyboard interrupt received")
        finally:
            self.stop()
        
        return 0


class PreforkASGIServer(CommonServer):
    """
    Prefork ASGI server - equivalent to PreforkServer
    Uses multiprocessing with ASGI workers
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.population = config['workers']
        self.timeout = config['limit_time_real']
        self.limit_request = config['limit_request']
        self.workers = {}
        self.generation = 0
        self.master_pid = os.getpid()
    
    def start(self):
        """Start the prefork ASGI server"""
        _logger.info('ASGI Prefork server running on %s:%s with %d workers', 
                    self.interface, self.port, self.population)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGCHLD, self._signal_handler)
        
        # Start worker processes
        for i in range(self.population):
            self._spawn_worker(i)
        
        # Master process loop
        try:
            while True:
                self._manage_workers()
                time.sleep(1)
        except KeyboardInterrupt:
            _logger.info("Master process received keyboard interrupt")
        finally:
            self._shutdown_workers()
    
    def _spawn_worker(self, worker_id):
        """Spawn a worker process"""
        pid = os.fork()
        if pid == 0:
            # Child process - become worker
            self._worker_main(worker_id)
        else:
            # Parent process - track worker
            self.workers[pid] = {
                'id': worker_id,
                'pid': pid,
                'started': time.time(),
                'requests': 0
            }
            _logger.info("Started ASGI worker %d (PID: %d)", worker_id, pid)
    
    def _worker_main(self, worker_id):
        """Main function for worker processes"""
        try:
            # Setup worker-specific configuration
            worker_config = Config(
                app=self.app,
                host=self.interface,
                port=self.port + worker_id,  # Each worker gets its own port
                log_level="info",
                access_log=True,
                use_colors=False,
                server_header=False,
                date_header=False,
                install_signal_handlers=True,
            )
            
            server = Server(worker_config)
            asyncio.run(server.serve())
            
        except Exception:
            _logger.exception("Exception in ASGI worker %d", worker_id)
        finally:
            os._exit(0)
    
    def _manage_workers(self):
        """Manage worker processes"""
        # Check for dead workers and restart them
        for pid in list(self.workers.keys()):
            try:
                os.waitpid(pid, os.WNOHANG)
            except OSError:
                # Worker is dead, restart it
                worker_info = self.workers.pop(pid)
                _logger.warning("Worker %d (PID: %d) died, restarting", 
                              worker_info['id'], pid)
                self._spawn_worker(worker_info['id'])
    
    def _signal_handler(self, signum, frame):
        """Handle signals"""
        if signum in (signal.SIGINT, signal.SIGTERM):
            _logger.info("Master process received signal %d, shutting down", signum)
            self._shutdown_workers()
            os._exit(0)
        elif signum == signal.SIGCHLD:
            # Child process died, will be handled in _manage_workers
            pass
    
    def _shutdown_workers(self):
        """Shutdown all worker processes"""
        for pid in list(self.workers.keys()):
            try:
                os.kill(pid, signal.SIGTERM)
                os.waitpid(pid, 0)
            except OSError:
                pass
        self.workers.clear()
    
    def stop(self):
        """Stop the prefork server"""
        self._shutdown_workers()
        super().stop()
    
    def run(self, preload, stop):
        """Run the server"""
        if stop:
            return 0
        
        try:
            self.start()
        except KeyboardInterrupt:
            _logger.info("Prefork server received keyboard interrupt")
        finally:
            self.stop()
        
        return 0
