# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async PostgreSQL connector using asyncpg for high-performance async database operations.
This module provides async equivalents to the synchronous database operations in sql_db.py
while maintaining API compatibility for seamless migration.
"""
from __future__ import annotations

import asyncio
import logging
import os
import re
import threading
import time
import typing
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timedelta, timezone
from inspect import currentframe

import asyncpg
from werkzeug import urls

import odoo
from odoo.tools import config

# Import asyncpg exceptions and create psycopg2-compatible aliases
from asyncpg.exceptions import (
    PostgresError as AsyncPostgresError,
    InterfaceError as AsyncInterfaceError,
    DataError as AsyncDataError,
    OperationalError as AsyncOperationalError,
    IntegrityError as AsyncIntegrityError,
    InternalError as AsyncInternalError,
    ProgrammingError as AsyncProgrammingError,
    NotSupportedError as AsyncNotSupportedError,
)

# Create psycopg2-compatible exception classes
class Error(Exception):
    """Base exception for database errors."""
    pass

class Warning(Exception):
    """Warning exception."""
    pass

class InterfaceError(Error):
    """Interface error exception."""
    pass

class DatabaseError(Error):
    """Database error exception."""
    pass

class DataError(DatabaseError):
    """Data error exception."""
    pass

class OperationalError(DatabaseError):
    """Operational error exception."""
    pass

class IntegrityError(DatabaseError):
    """Integrity error exception."""
    pass

class InternalError(DatabaseError):
    """Internal error exception."""
    pass

class ProgrammingError(DatabaseError):
    """Programming error exception."""
    pass

class NotSupportedError(DatabaseError):
    """Not supported error exception."""
    pass

# Exception mapping from asyncpg to psycopg2-compatible
ASYNCPG_TO_PSYCOPG2_EXCEPTIONS = {
    AsyncPostgresError: DatabaseError,
    AsyncInterfaceError: InterfaceError,
    AsyncDataError: DataError,
    AsyncOperationalError: OperationalError,
    AsyncIntegrityError: IntegrityError,
    AsyncInternalError: InternalError,
    AsyncProgrammingError: ProgrammingError,
    AsyncNotSupportedError: NotSupportedError,
}

_logger = logging.getLogger(__name__)

# SQL query logging
sql_counter = 0

def real_time():
    """Return the current time in seconds since epoch."""
    return time.time()

class AsyncCallbacks:
    """Async version of the Callbacks class for managing async hooks."""
    
    def __init__(self):
        self._callbacks = []
    
    def add(self, callback):
        """Add an async callback function."""
        self._callbacks.append(callback)
    
    async def run(self):
        """Run all callbacks asynchronously."""
        for callback in self._callbacks:
            if asyncio.iscoroutinefunction(callback):
                await callback()
            else:
                # Run sync callback in thread pool
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, callback)
    
    def clear(self):
        """Clear all callbacks."""
        self._callbacks.clear()

class AsyncSavepoint:
    """Async savepoint context manager."""
    
    def __init__(self, cursor):
        self._cursor = cursor
        self._name = f"sp_{uuid.uuid4().hex}"
        self._closed = False
    
    async def __aenter__(self):
        await self._cursor.execute(f"SAVEPOINT {self._name}")
        return self
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        if not self._closed:
            if exc_type is None:
                await self._cursor.execute(f"RELEASE SAVEPOINT {self._name}")
            else:
                await self._cursor.execute(f"ROLLBACK TO SAVEPOINT {self._name}")
            self._closed = True
    
    async def close(self, rollback=False):
        """Close the savepoint."""
        if not self._closed:
            if rollback:
                await self._cursor.execute(f"ROLLBACK TO SAVEPOINT {self._name}")
            else:
                await self._cursor.execute(f"RELEASE SAVEPOINT {self._name}")
            self._closed = True

class AsyncFlushingSavepoint(AsyncSavepoint):
    """Async savepoint that flushes before entering and clears on exit."""
    
    async def __aenter__(self):
        await self._cursor.flush()
        return await super().__aenter__()
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            return await super().__aexit__(exc_type, exc_value, traceback)
        finally:
            if exc_type is None:
                await self._cursor.flush()
            else:
                self._cursor.clear()

class AsyncBaseCursor:
    """Base class for async cursors that manage pre/post commit hooks."""
    
    def __init__(self):
        self.precommit = AsyncCallbacks()
        self.postcommit = AsyncCallbacks()
        self.prerollback = AsyncCallbacks()
        self.postrollback = AsyncCallbacks()
        self.transaction = None
    
    async def flush(self):
        """Flush the current transaction, and run precommit hooks."""
        if self.transaction is not None:
            await self.transaction.flush()
        await self.precommit.run()
    
    def clear(self):
        """Clear the current transaction, and clear precommit hooks."""
        if self.transaction is not None:
            self.transaction.clear()
        self.precommit.clear()
    
    def reset(self):
        """Reset the current transaction."""
        if self.transaction is not None:
            self.transaction.reset()
    
    def savepoint(self, flush=True):
        """Context manager entering in a new savepoint."""
        if flush:
            return AsyncFlushingSavepoint(self)
        else:
            return AsyncSavepoint(self)
    
    async def __aenter__(self):
        """Using the cursor as an async context manager."""
        return self
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            if exc_type is None:
                await self.commit()
        finally:
            await self.close()

class AsyncCursor(AsyncBaseCursor):
    """Async cursor that wraps asyncpg connections with psycopg2 compatibility."""

    def __init__(self, pool, dbname, dsn):
        super().__init__()
        self.__pool = pool
        self.dbname = dbname
        self.__dsn = dsn
        self._cnx = None
        self._closed = False
        self._now = None
        self.sql_log_count = 0
        self._last_result = []
        self._result_index = 0
        self.rowcount = -1
        self.description = None
        self.arraysize = 1
        self.cache = {}
    
    async def _ensure_connection(self):
        """Ensure we have an active connection."""
        if self._cnx is None or self._cnx.is_closed():
            self._cnx = await self.__pool.borrow(self.__dsn)
    
    def _convert_query_and_params(self, query, params=None):
        """Convert psycopg2-style query and parameters to asyncpg format."""
        if not params:
            return query, []

        # Handle different parameter formats
        if isinstance(params, dict):
            # Named parameters - convert %(name)s to $1, $2, etc.
            import re
            param_names = re.findall(r'%\((\w+)\)s', query)
            converted_query = query
            converted_params = []

            for i, name in enumerate(param_names, 1):
                converted_query = converted_query.replace(f'%({name})s', f'${i}')
                converted_params.append(params[name])

            return converted_query, converted_params

        elif isinstance(params, (list, tuple)):
            # Positional parameters - convert %s to $1, $2, etc.
            converted_query = query
            param_count = 0
            while '%s' in converted_query:
                param_count += 1
                converted_query = converted_query.replace('%s', f'${param_count}', 1)

            return converted_query, list(params)

        return query, []

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a query asynchronously with psycopg2 compatibility."""
        global sql_counter

        if self._closed:
            raise InterfaceError("Cursor already closed")

        await self._ensure_connection()

        start = real_time()
        try:
            # Convert query and parameters
            converted_query, converted_params = self._convert_query_and_params(query, params)

            # Determine if this is a SELECT query
            is_select = query.strip().upper().startswith('SELECT')

            if is_select:
                # For SELECT queries, fetch results
                self._last_result = await self._cnx.fetch(converted_query, *converted_params)
                self.rowcount = len(self._last_result)
                self._result_index = 0

                # Set description for compatibility
                if self._last_result:
                    # Create description tuple similar to psycopg2
                    first_row = self._last_result[0]
                    self.description = tuple(
                        (key, None, None, None, None, None, None)
                        for key in first_row.keys()
                    )
                else:
                    self.description = None
            else:
                # For non-SELECT queries, just execute
                result = await self._cnx.execute(converted_query, *converted_params)
                self._last_result = []
                self.rowcount = -1
                self.description = None

                # Extract rowcount from result if available
                if isinstance(result, str) and result.startswith(('INSERT', 'UPDATE', 'DELETE')):
                    parts = result.split()
                    if len(parts) >= 2 and parts[-1].isdigit():
                        self.rowcount = int(parts[-1])

        except Exception as e:
            # Convert asyncpg exceptions to psycopg2-compatible ones
            converted_exception = self._convert_exception(e)
            if log_exceptions:
                _logger.error("bad query: %s\nERROR: %s", query, converted_exception)
            raise converted_exception
        finally:
            delay = real_time() - start
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug("[%.3f ms] query: %s", 1000 * delay, self._format(query, params))

        self.sql_log_count += 1
        sql_counter += 1

        current_thread = threading.current_thread()
        if hasattr(current_thread, 'query_count'):
            current_thread.query_count += 1
            current_thread.query_time += delay

        # Optional hooks for performance and tracing analysis
        for hook in getattr(current_thread, 'query_hooks', ()):
            hook(self, query, params, start, delay)

    def _convert_exception(self, exception):
        """Convert asyncpg exceptions to psycopg2-compatible exceptions."""
        for asyncpg_exc, psycopg2_exc in ASYNCPG_TO_PSYCOPG2_EXCEPTIONS.items():
            if isinstance(exception, asyncpg_exc):
                # Create new exception with same message but psycopg2-compatible type
                new_exc = psycopg2_exc(str(exception))
                # Copy relevant attributes if they exist
                if hasattr(exception, 'pgcode'):
                    new_exc.pgcode = exception.pgcode
                if hasattr(exception, 'diag'):
                    new_exc.diag = exception.diag
                return new_exc

        # If no specific mapping found, return as generic DatabaseError
        return DatabaseError(str(exception))
    
    def fetchone(self):
        """Fetch one row from the last executed query (psycopg2 compatibility)."""
        if self._closed:
            raise InterfaceError("Cursor already closed")

        if not self._last_result or self._result_index >= len(self._last_result):
            return None

        row = self._last_result[self._result_index]
        self._result_index += 1

        # Convert asyncpg Record to tuple for psycopg2 compatibility
        if hasattr(row, 'values'):
            return tuple(row.values())
        return row

    def fetchmany(self, size=None):
        """Fetch multiple rows from the last executed query (psycopg2 compatibility)."""
        if self._closed:
            raise InterfaceError("Cursor already closed")

        if size is None:
            size = self.arraysize

        if not self._last_result or self._result_index >= len(self._last_result):
            return []

        end_index = min(self._result_index + size, len(self._last_result))
        rows = self._last_result[self._result_index:end_index]
        self._result_index = end_index

        # Convert asyncpg Records to tuples for psycopg2 compatibility
        result = []
        for row in rows:
            if hasattr(row, 'values'):
                result.append(tuple(row.values()))
            else:
                result.append(row)
        return result

    def fetchall(self):
        """Fetch all remaining rows from the last executed query (psycopg2 compatibility)."""
        if self._closed:
            raise InterfaceError("Cursor already closed")

        if not self._last_result or self._result_index >= len(self._last_result):
            return []

        rows = self._last_result[self._result_index:]
        self._result_index = len(self._last_result)

        # Convert asyncpg Records to tuples for psycopg2 compatibility
        result = []
        for row in rows:
            if hasattr(row, 'values'):
                result.append(tuple(row.values()))
            else:
                result.append(row)
        return result

    def dictfetchone(self):
        """Fetch one row as a dictionary (Odoo extension)."""
        if self._closed:
            raise InterfaceError("Cursor already closed")

        if not self._last_result or self._result_index >= len(self._last_result):
            return None

        row = self._last_result[self._result_index]
        self._result_index += 1

        # Convert asyncpg Record to dict
        if hasattr(row, '_asdict'):
            return row._asdict()
        elif hasattr(row, 'keys') and hasattr(row, 'values'):
            return dict(zip(row.keys(), row.values()))
        return row

    def dictfetchall(self):
        """Fetch all remaining rows as dictionaries (Odoo extension)."""
        if self._closed:
            raise InterfaceError("Cursor already closed")

        if not self._last_result or self._result_index >= len(self._last_result):
            return []

        rows = self._last_result[self._result_index:]
        self._result_index = len(self._last_result)

        # Convert asyncpg Records to dicts
        result = []
        for row in rows:
            if hasattr(row, '_asdict'):
                result.append(row._asdict())
            elif hasattr(row, 'keys') and hasattr(row, 'values'):
                result.append(dict(zip(row.keys(), row.values())))
            else:
                result.append(row)
        return result

    def mogrify(self, query, params=None):
        """Return the query string after parameter substitution (psycopg2 compatibility)."""
        if not params:
            return query.encode('utf-8')

        # Simple parameter substitution for compatibility
        # This is a basic implementation - asyncpg handles this internally
        converted_query, converted_params = self._convert_query_and_params(query, params)

        # Basic string substitution for logging/debugging purposes
        result_query = converted_query
        for i, param in enumerate(converted_params, 1):
            placeholder = f'${i}'
            if isinstance(param, str):
                value = f"'{param}'"
            elif param is None:
                value = 'NULL'
            else:
                value = str(param)
            result_query = result_query.replace(placeholder, value)

        return result_query.encode('utf-8')

    def copy_from(self, file, table, sep='\t', null='\\N', size=8192, columns=None):
        """Copy data from file to table (psycopg2 compatibility)."""
        # This is a simplified implementation
        # In a real implementation, you'd use asyncpg's copy_from_table
        raise NotSupportedError("copy_from not yet implemented for asyncpg")

    def copy_to(self, file, table, sep='\t', null='\\N', columns=None):
        """Copy data from table to file (psycopg2 compatibility)."""
        # This is a simplified implementation
        # In a real implementation, you'd use asyncpg's copy_to_table
        raise NotSupportedError("copy_to not yet implemented for asyncpg")

    def execute_values(self, query, argslist, template=None, page_size=100, fetch=False):
        """Execute query with multiple parameter sets (psycopg2.extras compatibility)."""
        # This is a simplified implementation
        # For now, we'll execute each query individually
        results = []
        for args in argslist:
            if fetch:
                # Execute and fetch results
                converted_query, converted_params = self._convert_query_and_params(query, args)
                # This would need to be implemented as an async method in practice
                raise NotSupportedError("execute_values with fetch not yet implemented for asyncpg")
            else:
                # Just execute
                asyncio.create_task(self.execute(query, args))

        return results if fetch else None

    def split_for_in_conditions(self, ids, size=0):
        """Split IDs for IN conditions (Odoo extension)."""
        # This is an Odoo-specific method, keep the same implementation
        if not size:
            size = 1000  # Default size

        ids = tuple(ids)
        for i in range(0, len(ids), size):
            yield ids[i:i + size]

    def _format(self, query, params=None):
        """Format query for logging (internal method)."""
        if not params:
            return query

        # Use mogrify for formatting
        try:
            return self.mogrify(query, params).decode('utf-8', 'replace')
        except Exception:
            return f"{query} with params {params}"

    @property
    def closed(self):
        """Check if cursor is closed (psycopg2 compatibility)."""
        return self._closed or (self._cnx and self._cnx.is_closed())

    @property
    def connection(self):
        """Get the connection object (psycopg2 compatibility)."""
        return self._cnx

    def __getattr__(self, name):
        """Delegate unknown attributes to the underlying connection."""
        if self._closed and name == '_cnx':
            raise InterfaceError("Cursor already closed")
        if hasattr(self._cnx, name):
            return getattr(self._cnx, name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    async def fetchmany(self, size=None):
        """Fetch many results from the last query."""
        if hasattr(self, '_last_result') and self._last_result:
            if size is None:
                size = self.arraysize if hasattr(self, 'arraysize') else 1
            result = self._last_result[:size]
            self._last_result = self._last_result[size:]
            return result
        return []

    async def execute_values(self, query, argslist, template=None, page_size=100, fetch=False):
        """Execute a query with multiple parameter sets."""
        await self._ensure_connection()

        # Process in batches based on page_size
        results = []
        for i in range(0, len(argslist), page_size):
            batch = argslist[i:i + page_size]
            for args in batch:
                if template:
                    # Use template if provided (for compatibility with psycopg2.extras.execute_values)
                    formatted_query = template % args if isinstance(args, dict) else template
                    if fetch:
                        result = await self.fetch(formatted_query)
                        results.extend(result)
                    else:
                        await self.execute(formatted_query)
                else:
                    if fetch:
                        result = await self.fetch(query, args)
                        results.extend(result)
                    else:
                        await self.execute(query, args)

        return results if fetch else None

    def mogrify(self, query, params=None):
        """Return the query string after parameter substitution."""
        if params:
            try:
                return query % params
            except (TypeError, ValueError):
                return query
        return query

    @property
    def rowcount(self):
        """Number of rows affected by the last operation."""
        # asyncpg doesn't provide rowcount in the same way
        # This would need to be tracked separately
        return -1

    @property
    def description(self):
        """Description of the columns in the last query result."""
        # This would need to be implemented based on asyncpg's column info
        return None
    
    def _format(self, query, params):
        """Format query for logging."""
        if params:
            try:
                return query % params
            except (TypeError, ValueError):
                return f"{query} with params {params}"
        return query
    
    async def close(self, leak=False):
        """Close the cursor and return connection to pool."""
        if not self._closed:
            self._closed = True
            if self._cnx and not self._cnx.is_closed():
                if leak:
                    # Mark connection as leaked
                    pass
                else:
                    await self.__pool.give_back(self._cnx)
    
    async def commit(self):
        """Perform an SQL COMMIT."""
        await self.flush()
        if self._cnx and not self._cnx.is_closed():
            # For asyncpg, we need to manage transactions explicitly
            if hasattr(self, '_transaction') and self._transaction:
                await self._transaction.commit()
                self._transaction = None
        self.clear()
        self._now = None
        self.prerollback.clear()
        self.postrollback.clear()
        await self.postcommit.run()

    async def rollback(self):
        """Perform an SQL ROLLBACK."""
        self.clear()
        self.postcommit.clear()
        await self.prerollback.run()
        if self._cnx and not self._cnx.is_closed():
            # For asyncpg, we need to manage transactions explicitly
            if hasattr(self, '_transaction') and self._transaction:
                await self._transaction.rollback()
                self._transaction = None
        self._now = None
        await self.postrollback.run()

    async def _ensure_transaction(self):
        """Ensure we have an active transaction."""
        await self._ensure_connection()
        if not hasattr(self, '_transaction') or not self._transaction:
            self._transaction = self._cnx.transaction()
            await self._transaction.start()
    
    @property
    def closed(self):
        return self._closed or (self._cnx and self._cnx.is_closed())
    
    @property
    def readonly(self):
        return bool(self.__pool.readonly)
    
    async def now(self):
        """Return the transaction's timestamp."""
        if self._now is None:
            result = await self.fetch("SELECT (now() AT TIME ZONE 'UTC')")
            self._now = result[0][0] if result else datetime.now(timezone.utc)
        return self._now


class AsyncTestCursor(AsyncBaseCursor):
    """Async test cursor for testing environments."""

    def __init__(self, cursor, lock, readonly, current_test=None):
        assert isinstance(cursor, AsyncBaseCursor)
        self.current_test = current_test
        super().__init__()
        self._now = None
        self._closed = False
        self._cursor = cursor
        self.readonly = readonly
        self._lock = lock
        self._savepoint = None

    async def _ensure_savepoint(self):
        """Ensure we have a savepoint for test isolation."""
        if not self._savepoint:
            self._savepoint = self._cursor.savepoint(flush=False)
            await self._savepoint.__aenter__()

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a query in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.execute(query, params, log_exceptions)

    async def fetch(self, query, params=None):
        """Fetch results in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetch(query, params)

    async def fetchone(self, query=None, params=None):
        """Fetch one result in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetchone(query, params)

    async def fetchall(self, query=None, params=None):
        """Fetch all results in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetchall(query, params)

    async def close(self):
        """Close the test cursor."""
        if not self._closed:
            try:
                await self.rollback()
                if self._savepoint:
                    await self._savepoint.close(rollback=False)
            finally:
                self._closed = True

    async def commit(self):
        """Simulate commit in test mode."""
        await self.flush()
        if self._savepoint:
            await self._savepoint.close(rollback=self.readonly)
            self._savepoint = None
        self.clear()
        self.prerollback.clear()
        self.postrollback.clear()
        # TestCursor ignores post-commit hooks by default

    async def rollback(self):
        """Simulate rollback in test mode."""
        self.clear()
        self.postcommit.clear()
        await self.prerollback.run()
        if self._savepoint:
            await self._savepoint.close(rollback=True)
            self._savepoint = None
        await self.postrollback.run()

    def __getattr__(self, name):
        """Delegate to the underlying cursor."""
        return getattr(self._cursor, name)

    async def now(self):
        """Return the transaction's timestamp for tests."""
        if self._now is None:
            self._now = datetime.now(timezone.utc)
        return self._now


class AsyncConnectionPool:
    """Async connection pool for managing asyncpg connections."""

    def __init__(self, maxconn=64, readonly=False):
        self._pool = None
        self._maxconn = max(maxconn, 1)
        self._readonly = readonly
        self._lock = asyncio.Lock()
        self._connection_info = None

    def __repr__(self):
        pool_size = self._pool.get_size() if self._pool else 0
        idle_size = self._pool.get_idle_size() if self._pool else 0
        mode = 'read-only' if self._readonly else 'read/write'
        return f"AsyncConnectionPool({mode};idle={idle_size}/size={pool_size}/max={self._maxconn})"

    @property
    def readonly(self):
        return self._readonly

    def _debug(self, msg, *args):
        _logger.debug(('%r ' + msg), self, *args)

    async def _create_pool(self, connection_info):
        """Create the asyncpg connection pool."""
        if self._pool is None:
            try:
                # Convert connection info to asyncpg format
                dsn_parts = []
                if 'host' in connection_info:
                    dsn_parts.append(f"host={connection_info['host']}")
                if 'port' in connection_info:
                    dsn_parts.append(f"port={connection_info['port']}")
                if 'database' in connection_info:
                    dsn_parts.append(f"database={connection_info['database']}")
                if 'user' in connection_info:
                    dsn_parts.append(f"user={connection_info['user']}")
                if 'password' in connection_info:
                    dsn_parts.append(f"password={connection_info['password']}")

                dsn = " ".join(dsn_parts)

                self._pool = await asyncpg.create_pool(
                    dsn,
                    min_size=1,
                    max_size=self._maxconn,
                    command_timeout=60,
                    server_settings={
                        'application_name': 'odoo_async',
                        'jit': 'off',  # Disable JIT for better compatibility
                    }
                )
                self._connection_info = connection_info
                self._debug('Created async connection pool with max_size=%d', self._maxconn)
            except Exception as e:
                _logger.error('Failed to create async connection pool: %s', e)
                raise

    async def borrow(self, connection_info):
        """Borrow a connection from the pool."""
        async with self._lock:
            if self._pool is None:
                await self._create_pool(connection_info)

            try:
                connection = await self._pool.acquire()
                self._debug('Borrowed connection from pool')
                return connection
            except Exception as e:
                _logger.error('Failed to acquire connection from pool: %s', e)
                raise

    async def give_back(self, connection, keep_in_pool=True):
        """Return a connection to the pool."""
        if self._pool and connection:
            try:
                if keep_in_pool and not connection.is_closed():
                    await self._pool.release(connection)
                    self._debug('Returned connection to pool')
                else:
                    await connection.close()
                    self._debug('Closed connection (not returned to pool)')
            except Exception as e:
                _logger.error('Error returning connection to pool: %s', e)

    async def close_all(self):
        """Close all connections in the pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None
            self._debug('Closed all connections in pool')


class AsyncConnection:
    """Async connection wrapper that provides cursor management."""

    def __init__(self, pool, dbname, dsn):
        self.__dbname = dbname
        self.__dsn = dsn
        self.__pool = pool

    @property
    def dsn(self):
        dsn = dict(self.__dsn)
        dsn.pop('password', None)
        return dsn

    @property
    def dbname(self):
        return self.__dbname

    def cursor(self):
        """Create a new async cursor."""
        _logger.debug('create async cursor to %r', self.dsn)
        return AsyncCursor(self.__pool, self.__dbname, self.__dsn)

    def __bool__(self):
        raise NotImplementedError()


def async_connection_info_for(db_or_uri, readonly=False):
    """Parse connection info for async connections."""
    # Reuse the existing connection_info_for logic from sql_db
    from odoo.sql_db import connection_info_for
    return connection_info_for(db_or_uri, readonly)


# Global async pools
_AsyncPool = None
_AsyncPool_readonly = None


async def async_db_connect(to, allow_uri=False, readonly=False):
    """Create an async database connection."""
    global _AsyncPool, _AsyncPool_readonly

    maxconn = config.get('db_maxconn_gevent', 64) if odoo.evented else config.get('db_maxconn', 64)

    if _AsyncPool is None and not readonly:
        _AsyncPool = AsyncConnectionPool(int(maxconn), readonly=False)
    if _AsyncPool_readonly is None and readonly:
        _AsyncPool_readonly = AsyncConnectionPool(int(maxconn), readonly=True)

    db, info = async_connection_info_for(to, readonly)
    if not allow_uri and db != to:
        raise ValueError('URI connections not allowed')

    return AsyncConnection(_AsyncPool_readonly if readonly else _AsyncPool, db, info)


async def close_async_pools():
    """Close all async connection pools."""
    global _AsyncPool, _AsyncPool_readonly

    if _AsyncPool:
        await _AsyncPool.close_all()
        _AsyncPool = None

    if _AsyncPool_readonly:
        await _AsyncPool_readonly.close_all()
        _AsyncPool_readonly = None
