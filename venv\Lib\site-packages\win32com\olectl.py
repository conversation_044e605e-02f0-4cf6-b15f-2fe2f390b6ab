"""Constants used by COM Controls

Hand created version of olectl.h constants.
"""

import winerror

FACILITY_CONTROL = 0xA


def MAKE_SCODE(sev, fac, code):
    return int((int(-sev) << 31) | ((fac) << 16) | (code))


def STD_CTL_SCODE(n):
    return MAKE_SCODE(winerror.SEVERITY_ERROR, FACILITY_CONTROL, n)


CTL_E_ILLEGALFUNCTIONCALL = STD_CTL_SCODE(5)
CTL_E_OVERFLOW = STD_CTL_SCODE(6)
CTL_E_OUTOFMEMORY = STD_CTL_SCODE(7)
CTL_E_DIVISIONBYZERO = STD_CTL_SCODE(11)
CTL_E_OUTOFSTRINGSPACE = STD_CTL_SCODE(14)
CTL_E_OUTOFSTACKSPACE = STD_CTL_SCODE(28)
CTL_E_BADFILENAMEORNUMBER = STD_CTL_SCODE(52)
CTL_E_FILENOTFOUND = STD_CTL_SCODE(53)
CTL_E_BADFILEMODE = STD_CTL_SCODE(54)
CTL_E_FILEALREADYOPEN = STD_CTL_SCODE(55)
CTL_E_DEVICEIOERROR = STD_CTL_SCODE(57)
CTL_E_FILEALREADYEXISTS = STD_CTL_SCODE(58)
CTL_E_BADRECORDLENGTH = STD_CTL_SCODE(59)
CTL_E_DISKFULL = STD_CTL_SCODE(61)
CTL_E_BADRECORDNUMBER = STD_CTL_SCODE(63)
CTL_E_BADFILENAME = STD_CTL_SCODE(64)
CTL_E_TOOMANYFILES = STD_CTL_SCODE(67)
CTL_E_DEVICEUNAVAILABLE = STD_CTL_SCODE(68)
CTL_E_PERMISSIONDENIED = STD_CTL_SCODE(70)
CTL_E_DISKNOTREADY = STD_CTL_SCODE(71)
CTL_E_PATHFILEACCESSERROR = STD_CTL_SCODE(75)
CTL_E_PATHNOTFOUND = STD_CTL_SCODE(76)
CTL_E_INVALIDPATTERNSTRING = STD_CTL_SCODE(93)
CTL_E_INVALIDUSEOFNULL = STD_CTL_SCODE(94)
CTL_E_INVALIDFILEFORMAT = STD_CTL_SCODE(321)
CTL_E_INVALIDPROPERTYVALUE = STD_CTL_SCODE(380)
CTL_E_INVALIDPROPERTYARRAYINDEX = STD_CTL_SCODE(381)
CTL_E_SETNOTSUPPORTEDATRUNTIME = STD_CTL_SCODE(382)
CTL_E_SETNOTSUPPORTED = STD_CTL_SCODE(383)
CTL_E_NEEDPROPERTYARRAYINDEX = STD_CTL_SCODE(385)
CTL_E_SETNOTPERMITTED = STD_CTL_SCODE(387)
CTL_E_GETNOTSUPPORTEDATRUNTIME = STD_CTL_SCODE(393)
CTL_E_GETNOTSUPPORTED = STD_CTL_SCODE(394)
CTL_E_PROPERTYNOTFOUND = STD_CTL_SCODE(422)
CTL_E_INVALIDCLIPBOARDFORMAT = STD_CTL_SCODE(460)
CTL_E_INVALIDPICTURE = STD_CTL_SCODE(481)
CTL_E_PRINTERERROR = STD_CTL_SCODE(482)
CTL_E_CANTSAVEFILETOTEMP = STD_CTL_SCODE(735)
CTL_E_SEARCHTEXTNOTFOUND = STD_CTL_SCODE(744)
CTL_E_REPLACEMENTSTOOLONG = STD_CTL_SCODE(746)

CONNECT_E_FIRST = MAKE_SCODE(winerror.SEVERITY_ERROR, winerror.FACILITY_ITF, 0x0200)
CONNECT_E_LAST = MAKE_SCODE(winerror.SEVERITY_ERROR, winerror.FACILITY_ITF, 0x020F)
CONNECT_S_FIRST = MAKE_SCODE(winerror.SEVERITY_SUCCESS, winerror.FACILITY_ITF, 0x0200)
CONNECT_S_LAST = MAKE_SCODE(winerror.SEVERITY_SUCCESS, winerror.FACILITY_ITF, 0x020F)

CONNECT_E_NOCONNECTION = CONNECT_E_FIRST + 0
CONNECT_E_ADVISELIMIT = CONNECT_E_FIRST + 1
CONNECT_E_CANNOTCONNECT = CONNECT_E_FIRST + 2
CONNECT_E_OVERRIDDEN = CONNECT_E_FIRST + 3

CLASS_E_NOTLICENSED = winerror.CLASSFACTORY_E_FIRST + 2
