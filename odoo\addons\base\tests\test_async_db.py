# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import asyncio
import logging
import unittest
from unittest.mock import patch, MagicMock

from odoo.tests import common
from odoo.tests.common import BaseCase

_logger = logging.getLogger(__name__)

class TestAsyncDatabase(BaseCase):
    """Test suite for async database operations."""
    
    def setUp(self):
        super().setUp()
        self.db_name = common.get_db_name()
    
    def test_async_import(self):
        """Test that async database module can be imported."""
        try:
            import odoo.async_sql_db
            self.assertTrue(True, "async_sql_db module imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import async_sql_db: {e}")
    
    def test_async_connection_creation(self):
        """Test async database connection creation."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=True)
            self.assertIsNotNone(connection)
            self.assertEqual(connection.dbname, self.db_name)
            return connection
        
        # Run the async test
        connection = asyncio.run(_test())
        self.assertIsNotNone(connection)
    
    def test_async_cursor_creation(self):
        """Test async cursor creation and basic operations."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=True)
            cursor = connection.cursor()
            
            self.assertIsNotNone(cursor)
            self.assertFalse(cursor.closed)
            
            # Test basic query
            await cursor.execute("SELECT 1 as test_value")
            result = await cursor.fetchone()
            self.assertIsNotNone(result)
            
            await cursor.close()
            self.assertTrue(cursor.closed)
        
        asyncio.run(_test())
    
    def test_async_transaction_management(self):
        """Test async transaction commit and rollback."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=False)
            cursor = connection.cursor()
            
            try:
                async with cursor:
                    # Test transaction operations
                    await cursor.execute("SELECT COUNT(*) FROM res_users")
                    initial_count = await cursor.fetchone()
                    self.assertIsNotNone(initial_count)
                    
                    # Test commit (implicit in context manager)
                    await cursor.commit()
                    
                    # Test rollback
                    await cursor.rollback()
                    
            finally:
                await cursor.close()
        
        asyncio.run(_test())
    
    def test_async_savepoint(self):
        """Test async savepoint functionality."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=False)
            cursor = connection.cursor()
            
            try:
                async with cursor:
                    # Test savepoint creation
                    savepoint = cursor.savepoint()
                    self.assertIsNotNone(savepoint)
                    
                    async with savepoint:
                        await cursor.execute("SELECT 1")
                        result = await cursor.fetchone()
                        self.assertIsNotNone(result)
                    
            finally:
                await cursor.close()
        
        asyncio.run(_test())
    
    def test_async_callbacks(self):
        """Test async callback functionality."""
        async def _test():
            from odoo.async_sql_db import AsyncCallbacks
            
            callbacks = AsyncCallbacks()
            test_value = []
            
            async def async_callback():
                test_value.append("async")
            
            def sync_callback():
                test_value.append("sync")
            
            callbacks.add(async_callback)
            callbacks.add(sync_callback)
            
            await callbacks.run()
            
            self.assertIn("async", test_value)
            self.assertIn("sync", test_value)
            
            callbacks.clear()
            self.assertEqual(len(callbacks._callbacks), 0)
        
        asyncio.run(_test())
    
    def test_migration_utils_import(self):
        """Test that migration utilities can be imported."""
        try:
            import odoo.async_migration_utils
            self.assertTrue(True, "async_migration_utils module imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import async_migration_utils: {e}")
    
    def test_async_to_sync_conversion(self):
        """Test async to sync function conversion."""
        from odoo.async_migration_utils import async_to_sync
        
        async def async_function(x):
            await asyncio.sleep(0.001)  # Simulate async work
            return x * 2
        
        sync_function = async_to_sync(async_function)
        result = sync_function(5)
        self.assertEqual(result, 10)
    
    def test_sync_to_async_conversion(self):
        """Test sync to async function conversion."""
        from odoo.async_migration_utils import sync_to_async
        
        def sync_function(x):
            return x * 3
        
        async_function = sync_to_async(sync_function)
        
        async def _test():
            result = await async_function(4)
            return result
        
        result = asyncio.run(_test())
        self.assertEqual(result, 12)
    
    def test_auto_async_compat_decorator(self):
        """Test the auto async compatibility decorator."""
        from odoo.async_migration_utils import auto_async_compat
        
        @auto_async_compat
        async def test_function(x):
            await asyncio.sleep(0.001)
            return x + 1
        
        # Test async usage
        async def _test_async():
            result = await test_function(5)
            return result
        
        async_result = asyncio.run(_test_async())
        self.assertEqual(async_result, 6)
        
        # Test sync usage
        sync_result = test_function.sync_version(5)
        self.assertEqual(sync_result, 6)
    
    def test_async_compat_cursor(self):
        """Test the async compatibility cursor wrapper."""
        from odoo.async_migration_utils import AsyncCompatCursor
        
        # Mock async cursor
        mock_async_cursor = MagicMock()
        mock_async_cursor.execute = MagicMock()
        mock_async_cursor.__aenter__ = MagicMock()
        mock_async_cursor.__aexit__ = MagicMock()
        
        # Create compatibility wrapper
        compat_cursor = AsyncCompatCursor(mock_async_cursor)
        
        # Test that we can access the wrapped cursor
        self.assertIsNotNone(compat_cursor._async_cursor)
    
    def test_database_compatibility_layer(self):
        """Test the database compatibility layer."""
        async def _test():
            from odoo.asgi_compat import DatabaseCompatibility
            
            # Test async cursor creation
            try:
                cursor = await DatabaseCompatibility.get_async_cursor(self.db_name, readonly=True)
                self.assertIsNotNone(cursor)
                await cursor.close()
            except ImportError:
                # Skip if async_sql_db is not available
                self.skipTest("async_sql_db not available")
        
        asyncio.run(_test())
    
    def test_connection_pool_management(self):
        """Test async connection pool management."""
        async def _test():
            from odoo.async_sql_db import AsyncConnectionPool, async_connection_info_for
            
            pool = AsyncConnectionPool(maxconn=2, readonly=True)
            self.assertEqual(pool._maxconn, 2)
            self.assertTrue(pool.readonly)
            
            # Test pool representation
            repr_str = repr(pool)
            self.assertIn("AsyncConnectionPool", repr_str)
            self.assertIn("read-only", repr_str)
        
        asyncio.run(_test())
    
    def test_async_execute_values(self):
        """Test async execute_values functionality."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=True)
            cursor = connection.cursor()
            
            try:
                # Test execute_values with multiple parameter sets
                query = "SELECT %s as value"
                argslist = [(1,), (2,), (3,)]
                
                results = await cursor.execute_values(query, argslist, fetch=True)
                self.assertIsNotNone(results)
                
            finally:
                await cursor.close()
        
        asyncio.run(_test())
    
    def test_error_handling(self):
        """Test error handling in async database operations."""
        async def _test():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(self.db_name, readonly=True)
            cursor = connection.cursor()
            
            try:
                # Test invalid query
                with self.assertRaises(Exception):
                    await cursor.execute("INVALID SQL QUERY")
                    
            finally:
                await cursor.close()
        
        asyncio.run(_test())
    
    def test_connection_info_parsing(self):
        """Test connection info parsing for async connections."""
        from odoo.async_sql_db import async_connection_info_for
        
        db_name, connection_info = async_connection_info_for(self.db_name)
        self.assertEqual(db_name, self.db_name)
        self.assertIsInstance(connection_info, dict)
    
    def test_async_pool_cleanup(self):
        """Test async connection pool cleanup."""
        async def _test():
            from odoo.async_sql_db import close_async_pools
            
            # Test pool cleanup (should not raise errors)
            await close_async_pools()
        
        asyncio.run(_test())


class TestAsyncDatabaseIntegration(BaseCase):
    """Integration tests for async database with existing Odoo functionality."""
    
    def test_async_with_existing_registry(self):
        """Test async database operations with existing registry."""
        async def _test():
            from odoo.asgi_compat import DatabaseCompatibility
            
            # Test getting registry asynchronously
            registry = await DatabaseCompatibility.get_registry(self.env.cr.dbname)
            self.assertIsNotNone(registry)
        
        asyncio.run(_test())
    
    def test_migration_helper_batch_queries(self):
        """Test batch query migration helper."""
        from odoo.async_migration_utils import MigrationHelper
        
        queries = [
            ("SELECT 1 as test", []),
            ("SELECT 2 as test", []),
            ("SELECT 3 as test", []),
        ]
        
        try:
            results = MigrationHelper.batch_migrate_queries(queries, self.env.cr.dbname)
            self.assertIsNotNone(results)
            self.assertEqual(len(results), 3)
        except ImportError:
            self.skipTest("async_sql_db not available for batch migration test")
