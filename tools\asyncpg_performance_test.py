#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AsyncPG Performance Validation Script

This script validates the performance improvements of asyncpg over psycopg2
and ensures all Odoo features work correctly with the new database layer.
"""

import time
import asyncio
import statistics
import sys
import os
from contextlib import contextmanager

# Add Odoo to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import odoo
    from odoo.sql_db import db_connect
    from odoo.async_sql_db import async_db_connect
    from odoo.async_migration_utils import create_sync_connection
except ImportError as e:
    print(f"Error importing Odoo modules: {e}")
    sys.exit(1)


class PerformanceValidator:
    """Validates asyncpg performance and functionality."""
    
    def __init__(self, db_name='postgres'):
        self.db_name = db_name
        self.results = {}
        
    @contextmanager
    def timer(self, operation_name):
        """Context manager for timing operations."""
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            if operation_name not in self.results:
                self.results[operation_name] = []
            self.results[operation_name].append(duration)
    
    def test_basic_connectivity(self):
        """Test basic database connectivity."""
        print("Testing basic connectivity...")
        
        try:
            # Test sync connection
            with self.timer('sync_connection'):
                conn = create_sync_connection(database=self.db_name)
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                assert result[0] == 1
                cursor.close()
            
            print("✓ Sync connection test passed")
            
            # Test async connection
            async def test_async():
                with self.timer('async_connection'):
                    conn = await async_db_connect(self.db_name)
                    cursor = conn.cursor()
                    await cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    assert result[0] == 1
                    await cursor.close()
            
            asyncio.run(test_async())
            print("✓ Async connection test passed")
            
        except Exception as e:
            print(f"✗ Connectivity test failed: {e}")
            return False
        
        return True
    
    def test_query_performance(self, num_queries=100):
        """Test query execution performance."""
        print(f"Testing query performance ({num_queries} queries)...")
        
        try:
            # Test sync queries
            conn = create_sync_connection(database=self.db_name)
            cursor = conn.cursor()
            
            for i in range(num_queries):
                with self.timer('sync_query'):
                    cursor.execute("SELECT %s as test_value", (i,))
                    result = cursor.fetchone()
                    assert result[0] == i
            
            cursor.close()
            print("✓ Sync query performance test completed")
            
            # Test async queries
            async def test_async_queries():
                conn = await async_db_connect(self.db_name)
                cursor = conn.cursor()
                
                for i in range(num_queries):
                    with self.timer('async_query'):
                        await cursor.execute("SELECT %s as test_value", (i,))
                        result = cursor.fetchone()
                        assert result[0] == i
                
                await cursor.close()
            
            asyncio.run(test_async_queries())
            print("✓ Async query performance test completed")
            
        except Exception as e:
            print(f"✗ Query performance test failed: {e}")
            return False
        
        return True
    
    def test_batch_operations(self, batch_size=50):
        """Test batch operation performance."""
        print(f"Testing batch operations ({batch_size} records)...")
        
        try:
            conn = create_sync_connection(database=self.db_name)
            cursor = conn.cursor()
            
            # Create test table
            cursor.execute("""
                CREATE TEMPORARY TABLE test_batch (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    value INTEGER
                )
            """)
            
            # Test batch insert
            with self.timer('batch_insert'):
                for i in range(batch_size):
                    cursor.execute(
                        "INSERT INTO test_batch (name, value) VALUES (%s, %s)",
                        (f"Test Record {i}", i)
                    )
            
            # Test batch select
            with self.timer('batch_select'):
                cursor.execute("SELECT * FROM test_batch ORDER BY id")
                results = cursor.fetchall()
                assert len(results) == batch_size
            
            # Test batch update
            with self.timer('batch_update'):
                for i in range(batch_size):
                    cursor.execute(
                        "UPDATE test_batch SET value = %s WHERE id = %s",
                        (i * 2, i + 1)
                    )
            
            cursor.close()
            print("✓ Batch operations test completed")
            
        except Exception as e:
            print(f"✗ Batch operations test failed: {e}")
            return False
        
        return True
    
    def test_parameter_conversion(self):
        """Test parameter conversion functionality."""
        print("Testing parameter conversion...")
        
        try:
            from odoo.async_sql_db import AsyncCursor
            
            # Test the parameter conversion method
            cursor = AsyncCursor(None, self.db_name, {})
            
            # Test positional parameters
            query, params = cursor._convert_query_and_params(
                "SELECT %s, %s", (1, 'test')
            )
            assert query == "SELECT $1, $2"
            assert params == [1, 'test']
            
            # Test named parameters
            query, params = cursor._convert_query_and_params(
                "SELECT %(id)s, %(name)s", {'id': 1, 'name': 'test'}
            )
            assert query == "SELECT $1, $2"
            assert params == [1, 'test']
            
            print("✓ Parameter conversion test passed")
            
        except Exception as e:
            print(f"✗ Parameter conversion test failed: {e}")
            return False
        
        return True
    
    def test_exception_handling(self):
        """Test exception handling and mapping."""
        print("Testing exception handling...")
        
        try:
            from odoo.async_sql_db import (
                Error, InterfaceError, DatabaseError, OperationalError
            )
            
            # Test exception hierarchy
            assert issubclass(InterfaceError, Error)
            assert issubclass(DatabaseError, Error)
            assert issubclass(OperationalError, DatabaseError)
            
            # Test exception raising
            conn = create_sync_connection(database=self.db_name)
            cursor = conn.cursor()
            
            try:
                cursor.execute("SELECT * FROM non_existent_table")
                assert False, "Should have raised an exception"
            except Exception as e:
                # Should catch some form of database error
                pass
            
            cursor.close()
            print("✓ Exception handling test passed")
            
        except Exception as e:
            print(f"✗ Exception handling test failed: {e}")
            return False
        
        return True
    
    def test_transaction_handling(self):
        """Test transaction handling."""
        print("Testing transaction handling...")
        
        try:
            conn = create_sync_connection(database=self.db_name)
            cursor = conn.cursor()
            
            # Create test table
            cursor.execute("""
                CREATE TEMPORARY TABLE test_transaction (
                    id SERIAL PRIMARY KEY,
                    value INTEGER
                )
            """)
            
            # Test successful transaction
            with cursor:
                cursor.execute("INSERT INTO test_transaction (value) VALUES (%s)", (1,))
                cursor.execute("INSERT INTO test_transaction (value) VALUES (%s)", (2,))
            
            # Verify data was committed
            cursor.execute("SELECT COUNT(*) FROM test_transaction")
            count = cursor.fetchone()[0]
            assert count == 2
            
            cursor.close()
            print("✓ Transaction handling test passed")
            
        except Exception as e:
            print(f"✗ Transaction handling test failed: {e}")
            return False
        
        return True
    
    def print_performance_summary(self):
        """Print performance summary."""
        print("\n" + "="*60)
        print("PERFORMANCE SUMMARY")
        print("="*60)
        
        for operation, times in self.results.items():
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                
                print(f"{operation}:")
                print(f"  Average: {avg_time:.4f}s")
                print(f"  Min:     {min_time:.4f}s")
                print(f"  Max:     {max_time:.4f}s")
                print(f"  Count:   {len(times)}")
                print()
    
    def run_all_tests(self):
        """Run all validation tests."""
        print("AsyncPG Performance Validation")
        print("="*60)
        
        tests = [
            self.test_basic_connectivity,
            self.test_parameter_conversion,
            self.test_exception_handling,
            self.test_transaction_handling,
            lambda: self.test_query_performance(50),
            lambda: self.test_batch_operations(25),
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            print()
        
        print("="*60)
        print(f"RESULTS: {passed}/{total} tests passed")
        
        if passed == total:
            print("✓ All tests passed! AsyncPG migration is working correctly.")
        else:
            print("✗ Some tests failed. Please review the errors above.")
        
        self.print_performance_summary()
        
        return passed == total


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='AsyncPG Performance Validation')
    parser.add_argument('--db', default='postgres', help='Database name to test')
    parser.add_argument('--queries', type=int, default=50, help='Number of queries for performance test')
    parser.add_argument('--batch-size', type=int, default=25, help='Batch size for batch operations test')
    
    args = parser.parse_args()
    
    validator = PerformanceValidator(args.db)
    
    try:
        success = validator.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
